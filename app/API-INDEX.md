# 📚 Índice de APIs - Sistema Mulbin

## Descripción General

El sistema Mulbin ofrece **dos APIs complementarias** para diferentes tipos de integración. Cada una está optimizada para casos de uso específicos:

---

## 🌐 API REST - Para Backends Externos

### 📁 Documentación: [`API-REST-External-Backends.md`](./API-REST-External-Backends.md)

**¿Cuándo usar esta API?**

- ✅ Integración desde **backends externos** (PHP, Python, Node.js, Java, C#)
- ✅ Sistemas **legacy** que necesitan consumir datos de Mulbin
- ✅ **Microservicios** que requieren acceso a datos inmobiliarios
- ✅ **Aplicaciones móviles** nativas (iOS/Android)
- ✅ **Webhooks** y automatizaciones
- ✅ **Dashboards** de administración externos

**Características:**

- 🔑 Autenticación via **API Keys**
- 📄 Formato **JSON** estándar
- 🚀 **Endpoints HTTP** RESTful
- 📊 **CRUD completo** de usuarios y posts
- 👤 **Gestión avanzada** de perfiles de corretores
- 🖼️ **Soporte para avatares** y archivos
- 📈 **Métricas de engagement**

**Ejemplos de integración:**

```bash
# PHP - Crear usuario
POST /api/users
Authorization: ApiKey abc123...

# Python - Obtener posts
GET /api/posts?location=norte&type=venta

# Node.js - Verificar username
GET /api/users/check-username/mi_usuario
```

---

## ⚡ API DDP - Para Frontends Reactivos

### 📁 Documentación: [`API-DDP-Frontend-Clients.md`](./API-DDP-Frontend-Clients.md)

**¿Cuándo usar esta API?**

- ✅ **Frontends Vue.js** (aplicación principal)
- ✅ **Aplicaciones web** que requieren **datos en tiempo real**
- ✅ **Dashboards reactivos** con actualizaciones automáticas
- ✅ **Chats** y notificaciones en vivo
- ✅ **Interfaces colaborativas** para corretores
- ✅ **Apps Meteor** nativas

**Características:**

- 🔄 **Suscripciones reactivas** en tiempo real
- ⚡ **Sincronización automática** de datos
- 👥 **Sistema de usuarios** integrado de Meteor
- 📡 **Publicaciones** optimizadas
- 🔙 **Compatibilidad legacy** con métodos antiguos
- 💾 **Caché inteligente** del lado cliente

**Ejemplos de integración:**

```javascript
// Vue.js - Suscripción reactiva
ddp.subscribe("postsInmobiliarios", { location: "norte" });

// React - Método DDP
ddp.call("postsInmobiliarios.create", postData);

// Meteor - Publicación automática
Meteor.publish("userNotifications", function(limit) { ... });
```

---

## 🎯 Guía de Decisión

### Usa **API REST** si:

- 🏢 Tienes un **backend existente** (PHP, Laravel, Django, Express)
- 🔗 Necesitas **integrar** con sistemas externos
- 📱 Desarrollas una **app móvil** nativa
- 🤖 Implementas **automatizaciones** o bots
- 📊 Creas **reportes** o dashboards externos
- 🔐 Requieres **control granular** de autenticación

### Usa **API DDP** si:

- ⚡ Necesitas **datos en tiempo real** (notificaciones, chat)
- 🖥️ Desarrollas el **frontend principal** de Mulbin
- 🔄 Quieres **sincronización automática** de datos
- 👥 Usas el **sistema de usuarios** de Meteor
- 📊 Necesitas **suscripciones reactivas**
- 💾 Quieres **caché automático** del lado cliente

---

## 🔄 Compatibilidad entre APIs

Ambas APIs comparten la **misma base de datos** y son totalmente compatibles:

```
┌─────────────────┐    ┌─────────────────┐
│   API REST      │    │    API DDP      │
│  (Backends)     │    │  (Frontends)    │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────┐      ┌────────┘
                 │      │
         ┌───────▼──────▼───────┐
         │   Base de Datos      │
         │     MongoDB          │
         │   (Sincronizada)     │
         └──────────────────────┘
```

**Ejemplo de flujo híbrido:**

1. 🔧 **Backend PHP** crea un usuario via API REST
2. ⚡ **Frontend Vue.js** recibe la actualización via DDP automáticamente
3. 📱 **App móvil** consulta el nuevo usuario via API REST
4. 🔔 **Sistema de notificaciones** DDP notifica a usuarios conectados

---

## 🚀 Primeros Pasos

### Para Backend Externo (PHP/Python/Node.js):

1. 📖 Lee [`API-REST-External-Backends.md`](./API-REST-External-Backends.md)
2. 🔑 Obtén tu API Key del administrador
3. 🧪 Prueba endpoints con Postman/curl
4. 💻 Implementa usando los ejemplos de código

### Para Frontend Reactivo (Vue.js):

1. 📖 Lee [`API-DDP-Frontend-Clients.md`](./API-DDP-Frontend-Clients.md)
2. 📦 Instala DDP client: `npm install ddp`
3. 🔗 Conecta a: `DDP.connect("http://localhost:3000")`
4. 📡 Suscríbete a publicaciones necesarias

---

## 🌐 URLs Base

### Desarrollo:

- **API REST:** `http://localhost:3000/api/`
- **API DDP:** `http://localhost:3000/` (WebSocket)

### Producción:

- **API REST:** `https://ws-si.mulb.in:3000/api/`
- **API DDP:** `https://ws-si.mulb.in:3000/` (WebSocket)

---

## 🛠️ Herramientas Recomendadas

### Para API REST:

- 🧪 **Postman** - Testing de endpoints
- 📝 **Insomnia** - Cliente REST alternativo
- 🔍 **curl** - Testing desde terminal
- 📊 **Swagger** - Documentación interactiva

### Para API DDP:

- 🌐 **DDP Monitor** - Debug de suscripciones
- ⚡ **Meteor DevTools** - Extension de Chrome
- 📡 **Robomongo/Compass** - Explorador de MongoDB
- 🔧 **Meteor Toys** - Debugging avanzado

---

## 📞 Soporte y Contacto

- 📖 **Documentación técnica:** Este directorio
- 🐛 **Issues:** Sistema de tickets interno
- 💬 **Chat técnico:** Canal #api-support
- 📧 **Email:** <EMAIL>

---

## 🔄 Actualizaciones

- **REST API:** Última actualización en documentación detallada
- **DDP API:** Incluye métodos legacy para compatibilidad
- **Ambas APIs:** Totalmente sincronizadas y operativas

> **💡 Tip:** Si no estás seguro de qué API usar, la **API REST** es generalmente más fácil de implementar para integraciones rápidas, mientras que **DDP** ofrece la mejor experiencia para aplicaciones reactivas.

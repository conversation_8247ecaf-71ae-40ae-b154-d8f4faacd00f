# Ejemplo: Autenticación de Usuario Existente en Meteor

## Descripción

Este ejemplo muestra cómo autenticar un usuario que ya existe en Meteor desde un sistema padre (PHP) y generar un token para uso inmediato, sin necesidad de crear un nuevo usuario.

## Casos de Uso

1. **Sistema Híbrido**: Usuario se autentica en sistema padre PHP, necesita acceso a funcionalidades de Meteor
2. **Migración**: Usuarios ya existen en Meteor, sistema padre necesita integrar funcionalidades
3. **Single Sign-On**: Un login en el sistema padre debe dar acceso a todas las funcionalidades

## Flujo Completo

### 1. Usuario Existente en Meteor

Primero, asumimos que el usuario ya existe en Meteor (creado previamente):

```json
{
  "_id": "ybeoRERF4PBm4fDLS",
  "username": "corretor_juan",
  "emails": [{"address": "<EMAIL>"}],
  "profile": {
    "firstName": "<PERSON>",
    "lastName": "<PERSON>",
    "company": "Inmobiliaria ABC"
  },
  "status": "active"
}
```

### 2. Autenticación desde Sistema Padre

#### Opción A: Por Email

```bash
curl -X POST "http://localhost:3000/api/auth/login" \
  -H "Content-Type: application/json" \
  -H "Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb" \
  -d '{
    "email": "<EMAIL>"
  }'
```

#### Opción B: Por Username

```bash
curl -X POST "http://localhost:3000/api/auth/login" \
  -H "Content-Type: application/json" \
  -H "Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb" \
  -d '{
    "username": "corretor_juan"
  }'
```

#### Opción C: Por ID de Usuario (si ya lo tienes)

```bash
curl -X POST "http://localhost:3000/api/auth/generate-token" \
  -H "Content-Type: application/json" \
  -H "Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb" \
  -d '{
    "userId": "ybeoRERF4PBm4fDLS"
  }'
```

### 3. Respuesta con Token

```json
{
  "message": "Autenticación exitosa",
  "user": {
    "_id": "ybeoRERF4PBm4fDLS",
    "username": "corretor_juan",
    "emails": [{"address": "<EMAIL>"}],
    "profile": {
      "firstName": "Juan",
      "lastName": "Pérez",
      "company": "Inmobiliaria ABC",
      "verified": true
    }
  },
  "userId": "ybeoRERF4PBm4fDLS",
  "authToken": "PAnnazYe7AAyZ4OMGdtThMkGzPZmG3OyVnY22JksCTD",
  "tokenInfo": {
    "type": "Bearer",
    "usage": "Usar como 'Authorization: Bearer ...' en requests posteriores",
    "expiresAt": "2025-06-05T23:59:59.000Z",
    "generatedAt": "2025-06-05T01:15:43.063Z"
  }
}
```

### 4. Usar Token Inmediatamente

```bash
# Crear un post inmobiliario
curl -X POST "http://localhost:3000/api/posts" \
  -H "Authorization: Bearer PAnnazYe7AAyZ4OMGdtThMkGzPZmG3OyVnY22JksCTD" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "venta",
    "title": "Casa en venta - Zona Norte",
    "description": "Hermosa casa familiar",
    "price": 2500000,
    "location": "norte",
    "authorId": "ybeoRERF4PBm4fDLS"
  }'

# Actualizar perfil del usuario
curl -X PUT "http://localhost:3000/api/update-user/ybeoRERF4PBm4fDLS" \
  -H "Authorization: Bearer PAnnazYe7AAyZ4OMGdtThMkGzPZmG3OyVnY22JksCTD" \
  -H "Content-Type: application/json" \
  -d '{
    "profile": {
      "bio": "Especialista en propiedades de lujo",
      "lastActive": "2025-06-05T01:20:00.000Z"
    }
  }'
```

## Implementación en PHP

### Clase de Gestión de Autenticación

```php
<?php
class MulbinAuthManager {
    private $apiUrl = 'http://localhost:3000/api/';
    private $apiKey = 'c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb';
    
    // Autenticar usuario existente por email
    public function authenticateByEmail($email) {
        return $this->makeAuthRequest('auth/login', ['email' => $email]);
    }
    
    // Autenticar usuario existente por username
    public function authenticateByUsername($username) {
        return $this->makeAuthRequest('auth/login', ['username' => $username]);
    }
    
    // Generar token por ID de usuario
    public function generateTokenById($userId) {
        return $this->makeAuthRequest('auth/generate-token', ['userId' => $userId]);
    }
    
    private function makeAuthRequest($endpoint, $data) {
        $response = $this->makeRequest($endpoint, 'POST', $data);
        
        if ($response['status'] === 200) {
            // Guardar token en sesión
            $_SESSION['meteor_auth'] = [
                'token' => $response['data']['authToken'],
                'userId' => $response['data']['userId'],
                'user' => $response['data']['user'],
                'expiresAt' => $response['data']['tokenInfo']['expiresAt']
            ];
            
            return $response['data'];
        } else {
            throw new Exception('Error de autenticación: ' . $response['data']['error']);
        }
    }
    
    // Hacer request autenticado con token de usuario
    public function makeAuthenticatedRequest($endpoint, $method = 'GET', $data = null) {
        if (!isset($_SESSION['meteor_auth'])) {
            throw new Exception('Usuario no autenticado');
        }
        
        $headers = [
            'Authorization: Bearer ' . $_SESSION['meteor_auth']['token'],
            'Content-Type: application/json'
        ];
        
        return $this->makeRequest($endpoint, $method, $data, $headers);
    }
    
    private function makeRequest($endpoint, $method, $data = null, $customHeaders = null) {
        $url = $this->apiUrl . $endpoint;
        
        $headers = $customHeaders ?: [
            'Authorization: ApiKey ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        if ($data && in_array($method, ['POST', 'PUT'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return [
            'status' => $httpCode,
            'data' => json_decode($response, true)
        ];
    }
}
?>
```

### Ejemplo de Uso Completo

```php
<?php
session_start();

$authManager = new MulbinAuthManager();

try {
    // ESCENARIO: Usuario se autentica en sistema padre
    $userEmail = $_POST['email']; // Del formulario de login del sistema padre
    
    // Paso 1: Autenticar en Meteor
    echo "🔐 Autenticando usuario en Meteor...\n";
    $meteorAuth = $authManager->authenticateByEmail($userEmail);
    
    echo "✅ Usuario autenticado: {$meteorAuth['user']['username']}\n";
    echo "🏢 Empresa: {$meteorAuth['user']['profile']['company']}\n";
    echo "🔐 Token válido hasta: {$meteorAuth['tokenInfo']['expiresAt']}\n";
    
    // Paso 2: Usar token para operaciones inmediatas
    
    // Crear un post inmobiliario
    $newPost = [
        'type' => 'venta',
        'title' => 'Propiedad exclusiva',
        'description' => 'Hermosa casa en zona residencial',
        'price' => 3200000,
        'location' => 'norte',
        'authorId' => $meteorAuth['userId']
    ];
    
    echo "\n📝 Creando post inmobiliario...\n";
    $postResponse = $authManager->makeAuthenticatedRequest('posts', 'POST', $newPost);
    
    if ($postResponse['status'] === 201) {
        echo "✅ Post creado: {$postResponse['data']['post']['title']}\n";
    }
    
    // Actualizar perfil
    $profileUpdate = [
        'profile' => [
            'bio' => 'Actualizado desde sistema padre',
            'lastActive' => date('c')
        ]
    ];
    
    echo "\n👤 Actualizando perfil...\n";
    $updateResponse = $authManager->makeAuthenticatedRequest(
        'update-user/' . $meteorAuth['userId'], 
        'PUT', 
        $profileUpdate
    );
    
    if ($updateResponse['status'] === 200) {
        echo "✅ Perfil actualizado exitosamente\n";
    }
    
    // Obtener información actualizada del usuario
    echo "\n📊 Obteniendo información actualizada...\n";
    $userResponse = $authManager->makeAuthenticatedRequest(
        'users/' . $meteorAuth['userId']
    );
    
    if ($userResponse['status'] === 200) {
        $user = $userResponse['data']['user'];
        echo "✅ Usuario: {$user['profile']['firstName']} {$user['profile']['lastName']}\n";
        echo "📧 Email: {$user['emails'][0]['address']}\n";
        echo "🏢 Empresa: {$user['profile']['company']}\n";
        echo "📝 Bio: {$user['profile']['bio']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
```

## Ventajas de este Enfoque

- ✅ **No duplicación**: No necesitas crear usuarios nuevos
- ✅ **Autenticación única**: Un login da acceso a todo el sistema
- ✅ **Tokens seguros**: Usa el sistema nativo de Meteor
- ✅ **Flexibilidad**: Múltiples formas de identificar al usuario
- ✅ **Inmediato**: Token disponible para uso inmediato
- ✅ **Escalable**: Funciona con miles de usuarios existentes

## Casos de Uso Reales

1. **Portal Inmobiliario**: Sistema PHP principal + funcionalidades avanzadas en Meteor
2. **CRM Integrado**: Gestión de clientes en PHP + red social inmobiliaria en Meteor
3. **App Móvil**: Backend PHP + funcionalidades en tiempo real con Meteor
4. **Migración Gradual**: Mover funcionalidades de PHP a Meteor sin perder usuarios

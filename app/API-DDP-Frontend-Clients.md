# API Documentation - Meteor Backend

## Descripción General

Este es un backend Meteor refactorizado y modularizado para una plataforma inmobiliaria. El servidor funciona exclusivamente como backend, sirviendo datos a través de DDP (Distributed Data Protocol) para frontends externos.

## Estructura del Proyecto

```
app/
├── imports/
│   ├── api/
│   │   ├── posts-inmobiliarios/     # Módulo de publicaciones inmobiliarias
│   │   ├── comentarios-post/        # Módulo de comentarios
│   │   ├── notifications/           # Módulo de notificaciones
│   │   └── shared/                  # Utilidades y constantes compartidas
│   └── startup/
│       └── server/                  # Configuración del servidor
├── server/
│   └── main.js                      # Punto de entrada principal
└── tests/                           # Tests unitarios
```

## 🔄 Compatibilidad hacia Atrás

Para mantener compatibilidad con frontends existentes, se han mantenido los métodos con nombres legacy:

### Métodos Legacy Disponibles

#### Notificaciones

- `getUnreadNotificationsCount()` → `notifications.getUnreadCount()`
- `markNotificationAsRead(id)` → `notifications.markAsRead(id)`
- `markAllNotificationsAsRead()` → `notifications.markAllAsRead()`
- `deleteNotification(id)` → `notifications.remove(id)`
- `generarNotificacionesEjemplo(cantidad)` → `notifications.generateDemo(cantidad)`
- `limpiarNotificacionesEjemplo()` → `notifications.clearDemo()`

#### Posts Inmobiliarios

- `getTotalPostsCount(filtros)` → `postsInmobiliarios.count(filtros)`
- `crearPostInmobiliario(post)` → `postsInmobiliarios.create(post)`
- `toggleInteresadoPost(postId)` → `postsInmobiliarios.toggleInterest(postId)`

#### Comentarios

- `agregarComentarioPost(postId, text)` → `comentariosPost.create(postId, text)`

#### Datos de Demo

- `generarDatosEjemplo(cantidad)` → `demo.generatePostsData(cantidad)`
- `limpiarDatosEjemplo()` → `demo.clearAllData()`

> **Nota:** Se recomienda migrar gradualmente a los nuevos nombres de métodos con namespace para mejor organización del código.

## Conexión DDP

### Conectar desde frontend externo

```javascript
import { DDP } from "meteor/ddp-client";

const ddp = DDP.connect("http://localhost:3000");
```

## API Endpoints

### 🏠 Posts Inmobiliarios

#### Publicaciones (Publications)

**`postsInmobiliarios`**

```javascript
ddp.subscribe("postsInmobiliarios", filtros, page, limit);

// Parámetros:
// - filtros: { type?, location?, maxPrice?, authorId? }
// - page: number (default: 1)
// - limit: number (default: 5)
```

**`postInmobiliario.byId`**

```javascript
ddp.subscribe("postInmobiliario.byId", postId);
```

**`postsInmobiliarios.byUser`**

```javascript
ddp.subscribe("postsInmobiliarios.byUser", userId, limit);
```

#### Métodos (Methods)

**Crear Post**

```javascript
ddp.call('postsInmobiliarios.create', {
  type: 'venta' | 'renta' | 'socio' | 'intercambio',
  title: string,
  description: string,
  price: number,
  location: 'norte' | 'sur' | 'este' | 'oeste' | 'centro',
  bedrooms?: number,
  bathrooms?: number,
  area?: number,
  images?: string[]
});
```

**Actualizar Post**

```javascript
ddp.call("postsInmobiliarios.update", postId, updateData);
```

**Eliminar Post**

```javascript
ddp.call("postsInmobiliarios.remove", postId);
```

**Toggle Interés**

```javascript
ddp.call("postsInmobiliarios.toggleInterest", postId);
```

**Contar Posts**

```javascript
ddp.call("postsInmobiliarios.count", filtros);
```

**Verificar Interés**

```javascript
ddp.call("postsInmobiliarios.isInterested", postId);
```

### 💬 Comentarios

#### Publicaciones

**`comentariosPost`**

```javascript
ddp.subscribe("comentariosPost", postId, limit);
```

**`comentariosPost.byUser`**

```javascript
ddp.subscribe("comentariosPost.byUser", userId, limit);
```

#### Métodos

**Crear Comentario**

```javascript
ddp.call('comentariosPost.create', postId, text, parentCommentId?);
```

**Actualizar Comentario**

```javascript
ddp.call("comentariosPost.update", comentarioId, newText);
```

**Eliminar Comentario**

```javascript
ddp.call("comentariosPost.remove", comentarioId);
```

**Toggle Like**

```javascript
ddp.call("comentariosPost.toggleLike", comentarioId);
```

**Contar Comentarios**

```javascript
ddp.call("comentariosPost.count", postId);
```

**Obtener Comentarios Paginados**

```javascript
ddp.call("comentariosPost.getPaginated", postId, page, limit);
```

### 🔔 Notificaciones

#### Publicaciones

**`userNotifications`**

```javascript
ddp.subscribe("userNotifications", limit);
```

**`unreadNotifications`**

```javascript
ddp.subscribe("unreadNotifications", limit);
```

**`demoNotifications`** (Solo desarrollo)

```javascript
ddp.subscribe("demoNotifications", limit);
```

#### Métodos

**Crear Notificación**

```javascript
ddp.call('notifications.create', {
  userId: string,
  title: string,
  message: string,
  type: 'info' | 'success' | 'warning' | 'error',
  iconName: string,
  source: string,
  relatedId?: string,
  relatedType?: string,
  actionUrl?: string,
  metadata?: object
});
```

**Marcar como Leída**

```javascript
ddp.call("notifications.markAsRead", notificationId);
```

**Marcar Todas como Leídas**

```javascript
ddp.call("notifications.markAllAsRead");
```

**Eliminar Notificación**

```javascript
ddp.call("notifications.remove", notificationId);
```

**Eliminar Leídas**

```javascript
ddp.call("notifications.removeRead");
```

**Contar No Leídas**

```javascript
ddp.call("notifications.getUnreadCount");
```

**Obtener Estadísticas**

```javascript
ddp.call("notifications.getStats");
```

### 🧪 Métodos de Demo

**Generar Posts de Ejemplo**

```javascript
ddp.call("demo.generatePostsData", cantidad);
```

**Limpiar Todos los Datos**

```javascript
ddp.call("demo.clearAllData");
```

**Obtener Estadísticas**

```javascript
ddp.call("demo.getStats");
```

**Generar Notificaciones de Ejemplo**

```javascript
ddp.call("notifications.generateDemo", cantidad);
```

**Limpiar Notificaciones de Demo**

```javascript
ddp.call("notifications.clearDemo");
```

## Esquemas de Datos

### Post Inmobiliario

```javascript
{
  _id: string,
  type: 'venta' | 'renta' | 'socio' | 'intercambio',
  title: string,
  description: string,
  price: number,
  location: 'norte' | 'sur' | 'este' | 'oeste' | 'centro',
  date: Date,
  author: {
    id: string,
    name: string,
    avatar: string
  },
  images: string[],
  interestedCount: number,
  commentsCount: number,
  // Opcionales
  bedrooms?: number,
  bathrooms?: number,
  area?: number
}
```

### Comentario

```javascript
{
  _id: string,
  postId: string,
  author: {
    id: string,
    name: string,
    avatar: string
  },
  text: string,
  date: Date,
  likes: number,
  edited: boolean,
  editedAt?: Date,
  parentCommentId?: string
}
```

### Notificación

```javascript
{
  _id: string,
  userId: string,
  title: string,
  message: string,
  type: 'info' | 'success' | 'warning' | 'error',
  read: boolean,
  createdAt: Date,
  iconName: string,
  source: string,
  relatedId?: string,
  relatedType?: string,
  actionUrl?: string,
  metadata?: object
}
```

## Configuración y Despliegue

### Variables de Entorno

```bash
MONGO_URL=*****************************************************************
ROOT_URL=http://localhost:3000
PORT=3000
NODE_ENV=development|production
METEOR_ALLOW_SUPERUSER=true
```

### Docker Compose

```bash
# Desarrollo
docker-compose up meteor-dev

# Producción
docker-compose --profile prod up meteor-prod
```

## Seguridad

- Todas las operaciones requieren autenticación
- Validación de datos con `check()` package
- Reglas de seguridad con `allow()` rules
- CORS configurado para conexiones DDP externas

## Testing

```bash
# Ejecutar tests
meteor test --driver-package meteortesting:mocha

# Tests en watch mode
meteor test --driver-package meteortesting:mocha --watch
```

## Monitoreo y Logs

El servidor incluye logs detallados para:

- Inicialización de módulos
- Creación de índices de base de datos
- Registro de publicaciones y métodos
- Errores y excepciones

## Ejemplos de Uso Completo

### Conectar y obtener posts

```javascript
import { DDP } from "meteor/ddp-client";

const ddp = DDP.connect("http://localhost:3000");

// Suscribirse a posts
const subscription = ddp.subscribe(
  "postsInmobiliarios",
  {
    type: "venta",
    location: "norte",
  },
  1,
  10
);

// Obtener datos reactivos
const posts = ddp.collection("postsInmobiliarios").find({});

// Crear nuevo post
ddp.call(
  "postsInmobiliarios.create",
  {
    type: "venta",
    title: "Casa en venta",
    description: "Hermosa casa en zona residencial",
    price: 2500000,
    location: "norte",
  },
  (error, result) => {
    if (error) {
      console.error("Error:", error);
    } else {
      console.log("Post creado:", result);
    }
  }
);
```

### Manejo de notificaciones

```javascript
// Suscribirse a notificaciones
ddp.subscribe("userNotifications", 20);

// Obtener notificaciones no leídas
ddp.call("notifications.getUnreadCount", (error, count) => {
  console.log("Notificaciones no leídas:", count);
});

// Marcar como leída
ddp.call("notifications.markAsRead", notificationId);
```

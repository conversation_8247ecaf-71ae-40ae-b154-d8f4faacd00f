# Meteor 3 Docker Images

Configuración Docker para aplicaciones Meteor 3 con integración de MongoDB.

## Características

- Imágenes Docker optimizadas para Meteor 3
- MongoDB en un contenedor separado con autenticación
- Entornos de desarrollo y producción
- Volúmenes para persistencia de datos

## Requisitos

- Docker
- Docker Compose

## Estructura

- `Dockerfile.dev`: Imagen para desarrollo con hot-reload
- `Dockerfile.prod`: Imagen de producción optimizada
- `docker-compose.yml`: Configuración de servicios
- `mongo-init/`: Scripts de inicialización de MongoDB
- `app/`: Tu aplicación Meteor

## Uso

### Desarrollo

Para iniciar el entorno de desarrollo:

```bash
docker compose up meteor-dev
```

Esto creará automáticamente una app Meteor 3 si no existe y la ejecutará en modo desarrollo.

### Producción

Para ejecutar en modo producción:

```bash
docker compose --profile prod up meteor-prod
```

### Variables de entorno

Puedes configurar las siguientes variables:

- `MONGO_INITDB_ROOT_USERNAME`: Usuario de MongoDB (default: meteoruser)
- `MONGO_INITDB_ROOT_PASSWORD`: Contraseña de MongoDB (default: meteorpassword)
- `MONGO_DB_NAME`: Nombre de la base de datos (default: meteorDev/meteorProd)

## MongoDB

MongoDB se ejecuta en un contenedor separado con:

- Autenticación activada
- Volúmenes para datos y configuración
- Scripts de inicialización personalizables
- Healthcheck para comprobar disponibilidad
